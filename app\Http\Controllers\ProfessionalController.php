<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class ProfessionalController extends Controller
{
    function index(){

        $approved_users = User::whereHas('roles', function($q){
            $q->where('name', 'professional');
        })->where("approval", 1)->where("registration_completed", 1)->get();
        
        $unapproved_users = User::whereHas('roles', function($q){
            $q->where('name', 'professional');
        })->where("approval", 0)->where("registration_completed", 1)->get();


        return view('professional.index', compact('unapproved_users', 'approved_users'));
    }

    function approve($ids){
        $user = User::whereHas('roles', function($q){
            $q->where('name', 'professional');
        })->where('ids', $ids)->firstOrFail();
        if($user->approval == 1){
            return redirect()->back()->with(['title'=>'Error','message'=>'Professional already approved','type'=>'error']);
        }
        $user->approval = 1;
        $user->save();
        return redirect()->back()->with(['title'=>'Done','message'=>'Professional approved successfully','type'=>'success']);
    }
    function changeStatus($ids){
        $user = User::whereHas('roles', function($q){
            $q->where('name', 'professional');
        })->where('ids', $ids)->firstOrFail();

        if($user->status == 1){
            $user->status = 0;
            $message = 'Professional deactivated successfully';
        }else{
            $user->status = 1;
            $message = 'Professional activated successfully';
        }
        $user->save();
        return redirect()->back()->with(['title'=>'Done','message'=>$message,'type'=>'success']);
    }
}
