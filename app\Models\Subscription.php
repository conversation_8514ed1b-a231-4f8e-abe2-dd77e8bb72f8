<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;
    protected $fillable = [
        'product_id',
        'pricing_id',
        'type',
        'name',
        'price',
        'booking_fee',
        'total_service',
        'status',
    ];

    public function details()
    {
        return $this->hasMany(SubscriptionDetail::class);
    }
}
