<section class="footer ">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 footer-border-bottom">
                <div class="row row-gap-md-0 row-gap-10">
                    <div class="col-md-6  border-right padding padding-inline">
                        <div class="mw-550px">
                            <a class="navbar-brand" href="<?php echo e(url('/')); ?>">
                                <!-- <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->logo ?? ''); ?>"
                                     class="logo h-50px" /> -->
                                <img src="<?php echo e(asset('website')); ?>/assets/images/footer_primary.svg"
                                    class="h-50px w-250px object-fit-contain" alt="logo">
                            </a>
                            <p class="fs-15  dull-gray mt-5">We ara a lorem ipsum dolor sit amet, consectetur adipiscing
                                elit, sed do
                                eiusmod tempor incididun. consectetur adipiscing elit, sed do eiusmod.</p>
                            <div class="d-flex gap-20 mt-10">
                                <div class="d-flex align-items-center gap-3">
                                    <?php echo $__env->make('svg.phone', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <div>
                                        <p class="m-0 fs-12 sora dull-gray">Have a question?</p>
                                        <a href="tel:************" class=" black fs-16 sora semi_bold">************</a>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center gap-3">
                                    <?php echo $__env->make('svg.email', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <div>
                                        <p class="m-0 fs-12 sora dull-gray">Contact us at</p>
                                        <a href="mailto:<EMAIL>"
                                            class=" black fs-16 sora semi_bold"><EMAIL></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 padding padding-inline newsletter-footer">
                        <p class="nunito-sans bold black">Newsletter</p>
                        <p class="fs-15 dull-gray">Be the first one to know about discounts, offers and events weekly in
                            your mailbox. Unsubscribe whenever you like with one click.</p>

                        <form class="input-box d-flex justify-content-between align-items-center mt-10">
                            <div class="d-flex align-items-center email-input">
                                <label for="newsletter-email" class="visually-hidden">Email</label>
                                <!-- <i class="fa-regular fa-envelope me-2 fs-5"></i> -->
                                  <?php echo $__env->make('svg.email', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <input id="newsletter-email" type="email" placeholder="Enter your email" required>
                            </div>
                            <input type="submit" value="Submit">
                        </form>
                       
                    </div>
                </div>
            </div>
            <div class="col-md-12 padding-inline padding">
                <div class="d-flex justify-content-between">
                    <ul class="d-flex gap-10 p-0">
                        <!-- <li class="nav-item">
                            <a class="nav-link active fs-15 normal sora dull-gray" aria-current="page"
                               href="<?php echo e(url('/')); ?>">About us</a>
                        </li> -->
                        <li class="nav-item">
                            <a class="nav-link fs-15 normal sora dull-gray" href="<?php echo e(route('services')); ?>">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fs-15 normal sora dull-gray"
                                href="<?php echo e(route('professional')); ?>">Professional</a>
                        </li>
                    </ul>
                    <ul class="d-flex gap-10 p-0">
                        <li class="nav-item">
                            <a class="nav-link fs-15 normal sora dull-gray" href="<?php echo e(route('privacy_policy')); ?>">Privacy
                                policy</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fs-15 normal sora dull-gray" href="<?php echo e(route('terms')); ?>">Terms of Use</a>
                        </li>
                    </ul>
                </div>
                <div class="d-flex justify-content-between mt-6">
                    <div class="icons d-flex flex-wrap gap-6">
                        <a href="https://www.facebook.com/" target="_blank"  aria-label="Facebook">
                            <?php echo $__env->make('svg.facebook', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </a>

                        <a href="https://www.youtube.com/" target="_blank"  aria-label="Youtube ">
                            <?php echo $__env->make('svg.youtube', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </a>
                        <a href="https://www.whatsapp.com/" target="_blank"  aria-label="Whatsapp">
                            <?php echo $__env->make('svg.whatsapp', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </a>
                    </div>
                    <p class="fs-14 sora semi_bold black opacity">© stylenest 2025, All Rights Reserved</p>
                </div>

            </div>
        </div>
    </div>
</section>
<?php /**PATH D:\SALMAN\git\anders\resources\views/website/template/footer.blade.php ENDPATH**/ ?>