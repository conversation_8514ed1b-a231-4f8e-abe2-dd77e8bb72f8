<svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 35 35" fill="none">
  <g clip-path="url(#clip0_730_43523)">
    <path d="M17.5024 34.7344C17.4138 34.7344 17.3307 34.7288 17.2531 34.7178C17.1756 34.7067 17.0925 34.6956 17.0038 34.6845C16.9152 34.6734 16.8321 34.6568 16.7546 34.6347C16.677 34.6125 16.605 34.5848 16.5385 34.5516C16.472 34.5183 16.4 34.4851 16.3225 34.4518C16.2449 34.4186 16.1729 34.3743 16.1064 34.3189C16.0399 34.2635 15.9735 34.2136 15.907 34.1693C15.8405 34.125 15.774 34.0696 15.7075 34.0031L1.21562 19.478C0.728123 18.9905 0.484375 18.3977 0.484375 17.6997C0.484375 17.0017 0.728123 16.409 1.21562 15.9215L6.53376 10.6033C6.68887 10.4482 6.88276 10.3485 7.11543 10.3042C7.3481 10.2599 7.56415 10.2931 7.76358 10.4039C7.96301 10.5147 8.11812 10.6753 8.22891 10.8858C8.33971 11.0964 8.37295 11.3124 8.32863 11.534C8.28431 11.6891 8.25661 11.8276 8.24553 11.9495C8.23445 12.0713 8.22891 12.1877 8.22891 12.2985C8.22891 13.074 8.41726 13.7887 8.79397 14.4424C9.17067 15.096 9.68586 15.6168 10.3396 16.0046C10.9932 16.3923 11.6968 16.5862 12.4502 16.5862C13.6025 16.5862 14.5664 16.1707 15.3419 15.3398C16.1175 14.5088 16.5053 13.4951 16.5053 12.2985C16.5053 11.7223 16.4 11.1795 16.1895 10.6698C15.979 10.1601 15.6965 9.71143 15.3419 9.32364C14.9874 8.93586 14.5608 8.63118 14.0622 8.40959C13.5637 8.188 13.0263 8.07721 12.4502 8.07721C12.2508 8.07721 11.9959 8.11044 11.6857 8.17692C11.176 8.26556 10.7993 8.07721 10.5556 7.61187C10.3119 7.14653 10.3783 6.73659 10.755 6.38205L15.7075 1.39629C15.9513 1.17471 16.2283 1.00297 16.5385 0.8811C16.8487 0.759224 17.17 0.698288 17.5024 0.698288C17.8348 0.698288 18.1561 0.759224 18.4663 0.8811C18.7766 1.00297 19.0535 1.17471 19.2973 1.39629L22.9203 5.05251C23.297 3.85593 23.995 2.8754 25.0143 2.11092C26.0336 1.34644 27.1859 0.964195 28.4711 0.964195C29.2688 0.964195 30.0277 1.11931 30.7479 1.42953C31.4681 1.73976 32.0885 2.15524 32.6093 2.67597C33.13 3.1967 33.5455 3.82269 33.8557 4.55394C34.1659 5.28518 34.321 6.03859 34.321 6.81415C34.321 7.3238 34.2601 7.82792 34.1382 8.32649C34.0163 8.82507 33.8335 9.28487 33.5898 9.70588C33.346 10.1269 33.058 10.5091 32.7256 10.8526C32.3932 11.1961 32.0165 11.5008 31.5955 11.7667C31.1745 12.0326 30.7313 12.2431 30.266 12.3982L33.7892 15.9215C34.2767 16.409 34.5205 17.0017 34.5205 17.6997C34.5205 18.3977 34.2767 18.9905 33.7892 19.478L19.2973 34.0031C18.7876 34.4906 18.1893 34.7344 17.5024 34.7344ZM6.30109 13.8274L2.71135 17.4172C2.51192 17.6166 2.51192 17.805 2.71135 17.9822L17.2033 32.5074C17.4027 32.6847 17.6021 32.6847 17.8016 32.5074L32.2935 17.9822C32.3378 17.9379 32.371 17.8825 32.3932 17.816C32.4154 17.7496 32.4154 17.6776 32.3932 17.6C32.371 17.5224 32.3378 17.4615 32.2935 17.4172L27.1748 12.2985C26.8202 11.9439 26.7538 11.5395 26.9754 11.0853C27.1969 10.631 27.5515 10.4371 28.039 10.5036H28.2052C28.2273 10.5258 28.2384 10.5368 28.2384 10.5368C28.3492 10.5368 28.4268 10.5368 28.4711 10.5368C29.5125 10.5368 30.3934 10.1768 31.1135 9.4566C31.8337 8.73643 32.1938 7.86116 32.1938 6.83077C32.1938 5.80038 31.8337 4.91956 31.1135 4.18832C30.3934 3.45707 29.5125 3.09145 28.4711 3.09145C27.9836 3.09145 27.5072 3.19117 27.0418 3.3906C26.5765 3.59002 26.1776 3.85593 25.8452 4.18832C25.5129 4.5207 25.247 4.91956 25.0475 5.3849C24.8481 5.85023 24.7484 6.32665 24.7484 6.81415C24.7484 6.88062 24.7595 6.96926 24.7816 7.08006L24.8149 7.27948C24.837 7.41244 24.8259 7.55093 24.7816 7.69496C24.7373 7.839 24.6653 7.96641 24.5656 8.07721C24.4659 8.188 24.3495 8.27664 24.2166 8.34311C23.7734 8.5647 23.3745 8.49822 23.02 8.14368L17.8016 2.92526C17.7572 2.88094 17.7129 2.8477 17.6686 2.82554C17.6243 2.80338 17.5855 2.78677 17.5523 2.77569C17.519 2.76461 17.4858 2.76461 17.4526 2.77569C17.4193 2.78677 17.3805 2.80338 17.3362 2.82554C17.2919 2.8477 17.2476 2.88094 17.2033 2.92526L13.9792 6.14938C15.3752 6.48176 16.4997 7.21855 17.3528 8.35973C18.206 9.50092 18.6325 10.8138 18.6325 12.2985C18.6325 12.8746 18.5605 13.4397 18.4165 13.9936C18.2724 14.5476 18.0675 15.0573 17.8016 15.5226C17.5357 15.9879 17.2199 16.42 16.8543 16.8189C16.4886 17.2178 16.0787 17.5557 15.6245 17.8327C15.1702 18.1097 14.6716 18.3257 14.1287 18.4808C13.5858 18.6359 13.0263 18.7135 12.4502 18.7135C12.1621 18.7135 11.8741 18.6913 11.586 18.647C11.2979 18.6027 11.0154 18.5417 10.7384 18.4642C10.4614 18.3866 10.1955 18.2869 9.94069 18.165C9.68586 18.0432 9.44211 17.9102 9.20945 17.7662C8.97678 17.6222 8.74965 17.467 8.52806 17.3009C8.30647 17.1347 8.1015 16.9518 7.91315 16.7524C7.7248 16.553 7.54753 16.3369 7.38134 16.1043C7.21514 15.8716 7.06003 15.6334 6.916 15.3896C6.77197 15.1459 6.65009 14.8966 6.55038 14.6418C6.45066 14.387 6.36757 14.1155 6.30109 13.8274Z" fill="white"/>
  </g>
  <defs>
    <clipPath id="clip0_730_43523">
      <rect width="35" height="35" fill="white" transform="matrix(1 0 0 -1 0 35)"/>
    </clipPath>
  </defs>
</svg><?php /**PATH D:\SALMAN\git\anders\resources\views/svg/puzzle.blade.php ENDPATH**/ ?>