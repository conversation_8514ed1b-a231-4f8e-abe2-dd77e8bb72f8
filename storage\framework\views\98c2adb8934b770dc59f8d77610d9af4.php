<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid subscription">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-10">
                <div class="col-md-12">
                    <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                        <h6 class="sora black">Subscription Management</h6>
                    <?php endif; ?>
                    <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business')): ?>
                        <h6 class="sora black">Subscription</h6>
                    <?php endif; ?>
                    <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                </div>
                <div class="col-md-12">
                    <div class="Subscription-tabs">
                        <ul class="nav mb-5" id="myTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="Individual-tab" data-bs-toggle="tab"
                                    data-bs-target="#Individual" type="button" role="tab" aria-controls="Individual"
                                    aria-selected="true">
                                    <p class="fs-14 fw-500 mb-0">Individual</p>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="business-tab" data-bs-toggle="tab" data-bs-target="#business"
                                    type="button" role="tab" aria-controls="business" aria-selected="false">
                                    <p class="fs-14 fw-500 mb-0">Business</p>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="enterprise-tab" data-bs-toggle="tab"
                                    data-bs-target="#enterprise" type="button" role="tab" aria-controls="enterprise"
                                    aria-selected="false">
                                    <p class="fs-14 fw-500 mb-0">Enterprise</p>
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content " id="myTabContent">
                            <div class="tab-pane fade show active" id="Individual" role="tabpanel"
                                aria-labelledby="Individual-tab">

                                <div class="card_wrapper row">
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><i
                                                        class="fa-solid fa-star"></i>
                                                    Individual</h5>
                                            </div>
                                            <span class="badge current-plan-badge deep-blue fs-14 bold">Current Plan</span>

                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora">$149 <span
                                                    class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Full access to all features</li>
                                                    <li>Standard designed page</li>
                                                    <li>7.5% fee per booking</li>
                                                    <li>Access to Professional Portal</li>
                                                    <li>Access to Knowledge base</li>
                                                    <li>Customer service within 24h</li>
                                                </ul>
                                            </div>
                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <a href="#" class="cancel-link fs-16 semi_bold inter ">Cancel Your
                                                    Plan</a>
                                                <a href="#"
                                                    class="subscribed-label fs-16 semi_bold inter black">Subscribed</a>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Business Plan -->
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><span
                                                        class="me-3"><?php echo $__env->make('svg.business', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>Business</h5>
                                            </div>
                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora">$249 <span
                                                    class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Full access to all features</li>
                                                    <li>Standard designed page</li>
                                                    <li>5.0% fee per booking</li>
                                                    <li>Add multiple staff members</li>
                                                    <li>Access to Professional Portal</li>
                                                    <li>Access to Knowledge base</li>
                                                    <li>Customer service within 12h</li>
                                                </ul>
                                            </div>


                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn text-center">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <button class="fs-16 semi_bold inter white action-btn ">Upgrade Your
                                                    Plan</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Enterprise Plan -->
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><span
                                                        class="me-3"><?php echo $__env->make('svg.enterprise', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>Enterprise</h5>
                                            </div>
                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora"><span
                                                    class="fs-18 light-black normal sora">From</span>
                                                $500 <span class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Personalized customer support</li>
                                                    <li>Features tailored for your business</li>
                                                    <li>Unique profile page</li>
                                                    <li>2.5% fee per booking</li>
                                                    <li>Access to sponsored content</li>
                                                    <li>Priority Customer service</li>
                                                    <li>Part of outgoing Advertisement</li>
                                                </ul>
                                            </div>


                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn  text-center ">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <button class="fs-16 semi_bold inter white action-btn">Contact
                                                    Sales</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                            </div>


                            <div class="tab-pane fade show" id="business" role="tabpanel"
                                aria-labelledby="business-tab">

                                <div class="card_wrapper row">
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><i
                                                        class="fa-solid fa-star"></i>
                                                    Individual</h5>
                                            </div>
                                            <span class="badge current-plan-badge deep-blue fs-14 bold">Current Plan</span>

                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora">$149 <span
                                                    class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Full access to all features</li>
                                                    <li>Standard designed page</li>
                                                    <li>7.5% fee per booking</li>
                                                    <li>Access to Professional Portal</li>
                                                    <li>Access to Knowledge base</li>
                                                    <li>Customer service within 24h</li>
                                                </ul>
                                            </div>
                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <a href="#" class="cancel-link fs-16 semi_bold inter ">Cancel Your
                                                    Plan</a>
                                                <a href="#"
                                                    class="subscribed-label fs-16 semi_bold inter black">Subscribed</a>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Business Plan -->
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><span
                                                        class="me-3"><?php echo $__env->make('svg.business', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>Business</h5>
                                            </div>
                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora">$249 <span
                                                    class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Full access to all features</li>
                                                    <li>Standard designed page</li>
                                                    <li>5.0% fee per booking</li>
                                                    <li>Add multiple staff members</li>
                                                    <li>Access to Professional Portal</li>
                                                    <li>Access to Knowledge base</li>
                                                    <li>Customer service within 12h</li>
                                                </ul>
                                            </div>


                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn text-center">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <button class="fs-16 semi_bold inter white action-btn ">Upgrade Your
                                                    Plan</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Enterprise Plan -->
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><span
                                                        class="me-3"><?php echo $__env->make('svg.enterprise', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>Enterprise</h5>
                                            </div>
                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora"><span
                                                    class="fs-18 light-black normal sora">From</span>
                                                $500 <span class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Personalized customer support</li>
                                                    <li>Features tailored for your business</li>
                                                    <li>Unique profile page</li>
                                                    <li>2.5% fee per booking</li>
                                                    <li>Access to sponsored content</li>
                                                    <li>Priority Customer service</li>
                                                    <li>Part of outgoing Advertisement</li>
                                                </ul>
                                            </div>


                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn  text-center ">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <button class="fs-16 semi_bold inter white action-btn">Contact
                                                    Sales</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                            </div>


                            <div class="tab-pane fade show " id="enterprise" role="tabpanel"
                                aria-labelledby="enterprise-tab">


                                <div class="card_wrapper row">
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><i
                                                        class="fa-solid fa-star"></i>
                                                    Individual</h5>
                                            </div>
                                            <span class="badge current-plan-badge deep-blue fs-14 bold">Current Plan</span>

                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora">$149 <span
                                                    class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Full access to all features</li>
                                                    <li>Standard designed page</li>
                                                    <li>7.5% fee per booking</li>
                                                    <li>Access to Professional Portal</li>
                                                    <li>Access to Knowledge base</li>
                                                    <li>Customer service within 24h</li>
                                                </ul>
                                            </div>
                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <a href="#" class="cancel-link fs-16 semi_bold inter ">Cancel Your
                                                    Plan</a>
                                                <a href="#"
                                                    class="subscribed-label fs-16 semi_bold inter black">Subscribed</a>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Business Plan -->
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><span
                                                        class="me-3"><?php echo $__env->make('svg.business', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>Business</h5>
                                            </div>
                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora">$249 <span
                                                    class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Full access to all features</li>
                                                    <li>Standard designed page</li>
                                                    <li>5.0% fee per booking</li>
                                                    <li>Add multiple staff members</li>
                                                    <li>Access to Professional Portal</li>
                                                    <li>Access to Knowledge base</li>
                                                    <li>Customer service within 12h</li>
                                                </ul>
                                            </div>


                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn text-center">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <button class="fs-16 semi_bold inter white action-btn ">Upgrade Your
                                                    Plan</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Enterprise Plan -->
                                    <div class="col-md-4">
                                        <div class="card pricing-card">
                                            <div class="pricing-header d-flex justify-content-between align-item-center">
                                                <h5 class="fs-32 semi_bold sora card-text-blue"><span
                                                        class="me-3"><?php echo $__env->make('svg.enterprise', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>Enterprise</h5>
                                            </div>
                                            <p class="fs-16 regular light-black opacity-6 mt-2 pb-5">Perfect plan to get
                                                started</p>
                                            <h2 class="semi_bold black sora"><span
                                                    class="fs-18 light-black normal sora">From</span>
                                                $500 <span class="fs-18 light-black normal sora">/month</span></h2>
                                            <div class="card-body">
                                                <ul class="features">
                                                    <li>Personalized customer support</li>
                                                    <li>Features tailored for your business</li>
                                                    <li>Unique profile page</li>
                                                    <li>2.5% fee per booking</li>
                                                    <li>Access to sponsored content</li>
                                                    <li>Priority Customer service</li>
                                                    <li>Part of outgoing Advertisement</li>
                                                </ul>
                                            </div>


                                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                    class="fs-16 semi_bold inter white action-btn  text-center ">Edit
                                                    Package</a>
                                            <?php endif; ?>
                                            <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                <button class="fs-16 semi_bold inter white action-btn">Contact
                                                    Sales</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>

                    </div>
                </div>
            </div>
        </div>
        <?php echo $__env->make('dashboard.templates.modal.edit-package-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php $__env->stopSection(); ?>
    <?php $__env->startPush('js'); ?>
        <script>
            $(document).ready(function() {
                // Get stored tab
                var activeTab = localStorage.getItem('activeTab');
                if (activeTab) {
                    var tabElement = $('#myTab button[data-bs-target="' + activeTab + '"]');
                    if (tabElement.length) {
                        $('#myTab button').removeClass('active');
                        $('.tab-pane').removeClass('show active');

                        tabElement.addClass('active').attr('aria-selected', 'true');
                        $(activeTab).addClass('show active');
                    }
                }
                $('#myTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function(event) {
                    var target = $(event.target).attr('data-bs-target');
                    localStorage.setItem('activeTab', target);
                });
            });
        </script>

        <script>
            $(document).ready(function() {
                function updateModalForActiveTab() {
                    var activeTab = $('#myTab .nav-link.active');
                    if (activeTab.length > 0) {
                        var tabText = activeTab.find('p').text().trim();
                        var tabValue = tabText.toLowerCase();

                        $('#package-heading').html('<i class="fa-solid fa-star"></i> ' + tabText);
                        $('#package-type').val(tabValue);
                    }
                }
                // Update modal when it's shown
                $('#edit_package').on('show.bs.modal', function() {
                    updateModalForActiveTab();
                });
                $('#myTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function() {
                    updateModalForActiveTab();
                });
                updateModalForActiveTab();
            });
        </script>
    <?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/subscription/subscription.blade.php ENDPATH**/ ?>