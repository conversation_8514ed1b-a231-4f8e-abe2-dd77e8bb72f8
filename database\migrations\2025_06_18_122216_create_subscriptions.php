<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('ids');
            $table->string('product_id')->nullable();
            $table->string('pricing_id')->nullable();
            $table->string('type')->nullable();
            $table->string('name')->nullable();
            $table->integer('price')->nullable();
            $table->integer('booking_fee')->nullable();
            $table->integer('total_service')->nullable();
            $table->integer('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
