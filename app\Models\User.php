<?php

namespace App\Models;

use App\Traits\HasUuid;
use App\Models\GoogleCalendar;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, HasUuid;
    //use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array

     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array

     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array

     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function profile()
    {
        return $this->hasOne(Profile::class);
    }
    public function categories(){
        return $this->hasMany(UserCategory::class);
    }
    public function subcategories()
    {
        return $this->belongsToMany(SubCategory::class, 'user_categories', 'user_id', 'subcategory_id');
    }
    public function product_cerficates(){
        return $this->belongsToMany(Certification::class, 'user_product_certificates', 'user_id', 'certification_id');
    }
    public function certificates(){
        return $this->hasMany(UserCertificate::class);
    }
    function holidays()
    {
        return $this->hasMany(UserHoliday::class)->where("is_custom", 0);
    }
    function customHolidays()
    {
        return $this->hasMany(UserHoliday::class)->where("is_custom", 1);
    }
    function openingHours()
    {
        return $this->hasMany(UserOpeningHour::class);
    }
    public function googleCalendar()
    {
        return $this->hasOne(GoogleCalendar::class);
    }
    public function hasGoogleCalendarConnected()
    {
        return $this->googleCalendar && $this->googleCalendar->google_calendar_connected;
    }

    public function userSubscription()
    {
        return $this->hasOne(UserSubscription::class);
    }
}
