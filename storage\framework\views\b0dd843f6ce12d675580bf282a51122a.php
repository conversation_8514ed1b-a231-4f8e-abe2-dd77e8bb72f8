<!DOCTYPE html>
<html lang="en">

<head>
    <base href="" />
    <title><?php echo e(App\Models\Setting::first()->title ?? ''); ?></title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="<?php echo e(App\Models\Setting::first()->title ?? ''); ?>" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="<?php echo e(App\Models\Setting::first()->title ?? ''); ?>" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->favicon ?? ''); ?>" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"/>
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/rangeslider.js/2.3.3/rangeslider.css">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <?php echo $__env->yieldPushContent('css'); ?>
</head>

<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu" class="bg-body position-relative app-blank">
    <div id="secLoader">
        <div class="logo-loader">
            <div class="logo-container">
                <div class="circle">
                    <img src="<?php echo e(asset('website')); ?>/assets/images/logo-image.svg"
                        class="h-100 w-100 object-fit-contain" alt="logo">
                </div>
                <img src="<?php echo e(asset('website')); ?>/assets/images/v1_logo.svg" class="h-50 w-50 object-fit-contain"
                    alt="logo">
            </div>
        </div>
    </div>
    <div class="d-flex flex-column flex-root" id="kt_app_root">

        <?php echo $__env->make('website.template.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->yieldContent('content'); ?>
        <?php echo $__env->make('website.template.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    </div>
    <div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
        <i class="fa-solid fa-chevron-up"></i>
    </div>
    <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
    <?php echo $__env->yieldPushContent('js'); ?>

    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    
    <!-- jQuery (required) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>


    <script>
        //  <!-- Initialize Swiper -->
        var swiper = new Swiper(".rated-swipper", {
            slidesPerView: 4,
            spaceBetween: 20,
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });
        var swiper = new Swiper(".category-swipper", {
            slidesPerView: 6,
            spaceBetween: 20,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });
        var swiper = new Swiper(".service-swipper", {
            slidesPerView: 5,
            spaceBetween: 5,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });
        var swiper = new Swiper(".professional-swipper", {
            slidesPerView: 5,
            spaceBetween: 5,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });
        // select2
        $(document).ready(function () {
            $('.select2-filter').select2({
                minimumResultsForSearch: Infinity,
                width: 'auto',
                dropdownAutoWidth: true
            });
        });

        $(document).ready(function () {
            const navLinks = $('.table-content');
            const sections = $('.content > div[id]');

            // Scroll on click
            navLinks.on('click', function (e) {
                e.preventDefault();
                const targetId = $(this).attr('href');
                const offsetTop = $(targetId).offset().top - 20;

                $('html, body').stop().animate({ scrollTop: offsetTop }, 400);

                navLinks.removeClass('active');
                $(this).addClass('active');
            });

            // Scroll Spy
            $(window).on('scroll', function () {
                const scrollMiddle = $(window).scrollTop() + $(window).height() / 2;
                let currentId = '';

                sections.each(function () {
                    const sectionTop = $(this).offset().top;
                    const sectionBottom = sectionTop + $(this).outerHeight();

                    if (scrollMiddle >= sectionTop && scrollMiddle <= sectionBottom) {
                        currentId = $(this).attr('id');
                        return false; // break loop
                    }
                });

                if (currentId) {
                    navLinks.removeClass('active');
                    $('.table-content[href="#' + currentId + '"]').addClass('active');
                }
            });


            $(window).trigger('scroll');
        });
        // loader
        var loader = document.getElementById("secLoader");
        window.addEventListener("load", function () {
            loader.style.display = "none"
        });

        flatpickr("#datePicker", {
            dateFormat: "Y-m-d"
        });

        flatpickr("#timePicker", {
            enableTime: true,
            noCalendar: true,
            dateFormat: "h:i K",
            time_24hr: false
        });

        $(document).ready(function () {
            // Assign data-sub-category attributes to category boxes
            $('.service-drop-down #tab-personal-trainers .category-box').each(function (index) {
                const dataAttr = 'services-personal-trainer-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            $('.service-drop-down #tab-makeup-artists .category-box').each(function (index) {
                const dataAttr = 'services-makeup-artist-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            $('.professional-drop-down #tab-personal-trainers .category-box').each(function (index) {
                const dataAttr = 'professional-personal-trainer-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            // Redirect on category-box click (only if not already on target URL)
            $(document).on('click', '.category-box', function () {
                const $this = $(this);
                const subcategorySlug = $this.data('sub-category');
                const categoryPane = $this.closest('.tab-pane').attr('id');
                let targetUrl = '';

                if ($this.closest('.service-drop-down').length) {
                    targetUrl = '/services?category-name=' + categoryPane + '&subcategory-name=' + subcategorySlug;
                } else if ($this.closest('.professional-drop-down').length) {
                    targetUrl = '/professional?category-name=' + categoryPane + '&subcategory-name=' + subcategorySlug;
                }

                const fullTargetUrl = window.location.origin + targetUrl;

                if (targetUrl && window.location.href !== fullTargetUrl) {
                    window.location.href = targetUrl;
                }
            });

            // On page load: activate tab and scroll or click subcategory
            const params = new URLSearchParams(window.location.search);
            const categoryName = params.get('category-name');
            const subcategoryName = params.get('subcategory-name');

            if (categoryName) {
                const $mainTabBtn = $('[data-bs-target="#' + categoryName + '"]');
                const $subcategoryBox = $('[data-sub-category="' + subcategoryName + '"]');

                if ($mainTabBtn.length) {
                    $mainTabBtn.trigger('click');

                    if ($subcategoryBox.length) {
                        setTimeout(() => {
                            if ($subcategoryBox.hasClass('nav-link')) {
                                $subcategoryBox.trigger('click');
                            } else {
                                $subcategoryBox[0].scrollIntoView({ behavior: 'smooth' });
                            }

                            $subcategoryBox.addClass('active');
                            setTimeout(() => $subcategoryBox.removeClass('highlight'), 2000);
                        }, 300);
                    }
                }
            }
        });

        // favourite functionality
        $(document).ready(function () {
            $('.fav-icon i').on('click', function () {
                $(this).toggleClass('fa-solid fa-regular text-danger');
            });
        });

        $(document).ready(function () {
            $('.mega_menu_tabs').on('click', function () {
                // Deactivate all tabs
                $('.mega_menu_tabs').removeClass('active').attr('aria-selected', 'false').attr('tabindex', '-1');

                // Activate clicked tab
                $(this).addClass('active').attr('aria-selected', 'true').removeAttr('tabindex');

                // Get current path
                const path = window.location.pathname;

                if (!path.includes('/services')) {
                    $('#servicesLink').removeClass('active header-active');
                }
                if (!path.includes('/professional')) {
                    $('#professionalLink').removeClass('active header-active');
                }
            });
        });
    </script>
</body>

</html>
<?php /**PATH D:\SALMAN\git\anders\resources\views/website/layout/master.blade.php ENDPATH**/ ?>