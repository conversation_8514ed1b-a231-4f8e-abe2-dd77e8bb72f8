<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserSubscription extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'subscription_id',
        'subscription_type',
        'subscription_name',
        'subscription_price',
        'subscription_booking_fee',
        'subscription_total_service',
        'start_date',
        'end_date',
        'stripe_subscription_id',
        'status',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
