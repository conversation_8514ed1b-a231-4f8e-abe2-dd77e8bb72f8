<?php

namespace App\Http\Controllers;

use Stripe\Subscription;
use Stripe\Stripe;
use Stripe\Product;
use Stripe\Price;
use App\Models\Subscription as LocalSubscription;
use App\Models\SubscriptionDetail;
use App\Models\UserSubscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SubscriptionController extends Controller
{
    function __construct()
    {
         $this->middleware('permission:subscriptions-list|subscriptions-create|subscriptions-edit|subscriptions-delete', ['only' => ['index','store']]);
         $this->middleware('permission:subscriptions-create', ['only' => ['create','store']]);
         $this->middleware('permission:subscriptions-edit', ['only' => ['edit','update']]);
         $this->middleware('permission:subscriptions-delete', ['only' => ['destroy']]);
    }

    public function index()
    {
        $subscriptions = LocalSubscription::where('status',1)->get();
        return view('dashboard.subscription.subscription', compact('subscriptions'));
    }

    public function store(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $request->validate([
            "type" => "required",
            "name" => "required",
            "price" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "booking_fee" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "total_service" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
        ]);
        try {
            $product = Product::create([
                'name' => $request->name,
                'type' => 'service',
            ]);

            $price = Price::create([
                'unit_amount' => $request->price * 100,
                'currency' => 'usd',
                'recurring' => [
                    'interval' => 'month',
                ],
                'product' => $product->id,
            ]);
            $subscription = new LocalSubscription();
            $subscription->ids = Str::uuid();
            $subscription->product_id = $product->id;
            $subscription->pricing_id = $price->id;
            $subscription->type = $request->type;
            $subscription->name = $request->name;
            $subscription->price = $request->price;
            $subscription->booking_fee = $request->booking_fee;
            $subscription->total_service = $request->total_service;
            $subscription->status = 1;
            $subscription->save();
            if (isset($request->details)) {
                foreach ($request->details as $detailData) {
                    $detail = new SubscriptionDetail();
                    $detail->subscription_id = $subscription->id;
                    $detail->feature = $detailData['feature'];
                    $detail->save();
                }
            }
            return redirect()->back()->with([
                'message' => 'Subscription Plan created Successfully.',
                'title' => 'Success',
                'type' => 'success'
            ]);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            return back()->withError('Error creating subscription: ' . $e->getMessage());
        } catch (\Exception $e) {
            return back()->withError('An unexpected error occurred while creating the subscription.');
        }
    }

    public function edit($id)
    {
        $subscription = LocalSubscription::where("ids", $id)->firstOrFail();
        return response()->json($subscription);
    }

    public function update(Request $request, $id)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $request->validate([
            "type" => "required",
            "name" => "required",
            "price" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "booking_fee" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "total_service" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
        ]);
        $localSubscription = LocalSubscription::findOrFail($id);
        $oldPriceId = $localSubscription->pricing_id;
        try {
            $oldProduct = Product::retrieve($localSubscription->product_id);
            $newPrice = Price::create([
                'unit_amount' => $request->price * 100,
                'currency' => 'usd',
                'recurring' => [
                    'interval' => 'month',
                ],
                'product' => $oldProduct->id,
            ]);

            $localSubscription->type = $request->type;
            $localSubscription->name = $request->name;
            $localSubscription->booking_fee = $request->booking_fee;
            $localSubscription->total_service = $request->total_service;
            $localSubscription->pricing_id = $newPrice->id;
            $localSubscription->price = $request->price;
            $localSubscription->save();
            if (isset($request->details)) {
                SubscriptionDetail::where('subscription_id', $id)->delete();
                foreach ($request->details as $detailData) {
                    $detail = new SubscriptionDetail();
                    $detail->subscription_id = $id;
                    $detail->feature = $detailData['feature'];
                    $detail->save();
                }
            }
            if ($oldPriceId) {
                try {
                    $oldPrice = Price::retrieve($oldPriceId);
                    $oldPrice->updateAttributes(['active' => false]);
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    Log::warning('Error archiving old price: ' . $e->getMessage());
                }
            }
            $userSubscriptions = UserSubscription::with('stripe')->where('subscription_id', $localSubscription->id)->where('status', 1)->get();
            foreach ($userSubscriptions as $userSubscription) {
                try {
                    $stripeSubscription = Subscription::retrieve($userSubscription->stripe_subscription_id);
                    foreach ($stripeSubscription["items"]["data"] as $item) {
                        Subscription::update(
                            $stripeSubscription->id,
                            [
                                'items' => [
                                    [
                                        'id' => $item->id,
                                        'price' => $newPrice->id,
                                    ],
                                ],
                                'proration_behavior' => 'none',
                            ]
                        );
                    }
                    $this->user_notification($userSubscription->user_id, 'Subscription Updated', 'The plan you have subscribed has been updated');
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    Log::error('Error updating user subscription ' . $userSubscription->id . ': ' . $e->getMessage());
                }
            }
            return redirect()->back()->with([
                'message' => 'Subscription Plan updated Successfully.',
                'title' => 'Success',
                'type' => 'success'
            ]);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            return back()->withError('Error updating subscription: ' . $e->getMessage());
        } catch (\Exception $e) {
            return back()->withError('An unexpected error occurred while updating the subscription.');
        }
    }
}
