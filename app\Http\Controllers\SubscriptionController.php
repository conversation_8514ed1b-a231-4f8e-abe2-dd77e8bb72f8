<?php

namespace App\Http\Controllers;

use Stripe\Subscription;
use Stripe\Stripe;
use Stripe\Product;
use Stripe\Price;
use App\Models\Subscription as LocalSubscription;
use App\Models\SubscriptionDetail;
use App\Models\UserSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SubscriptionController extends Controller
{
    public function edit($id)
    {
        $subscription = LocalSubscription::where("ids", $id)->firstOrFail();
        return response()->json($subscription);
    }

    public function update(Request $request, $id)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $request->validate([
            "type" => "required",
            "name" => "required",
            "price" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "booking_fee" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "total_service" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
        ]);

        $localSubscription = LocalSubscription::findOrFail($id);
        $oldPriceId = $localSubscription->pricing_id;

        try {
            $oldProduct = Product::retrieve($localSubscription->product_id);
            $newPrice = Price::create([
                'unit_amount' => $request->price * 100,
                'currency' => 'usd',
                'recurring' => [
                    'interval' => 'month',
                ],
                'product' => $oldProduct->id,
            ]);

            $localSubscription->type = $request->type;
            $localSubscription->name = $request->name;
            $localSubscription->booking_fee = $request->booking_fee;
            $localSubscription->total_service = $request->total_service;
            $localSubscription->pricing_id = $newPrice->id;
            $localSubscription->price = $request->price;
            $localSubscription->save();
            if (isset($request->subscription_details)) {
                SubscriptionDetail::where('subscription_id', $id)->delete();
                foreach ($request->subscription_details as $detailData) {
                    $detail = new SubscriptionDetail();
                    $detail->subscription_id = $id;
                    $detail->feature = $detailData['feature'];
                    $detail->save();
                }
            }
            if ($oldPriceId) {
                try {
                    $oldPrice = Price::retrieve($oldPriceId);
                    $oldPrice->updateAttributes(['active' => false]);
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    Log::warning('Error archiving old price: ' . $e->getMessage());
                }
            }
            $userSubscriptions = UserSubscription::with('stripe')->where('subscription_id', $localSubscription->id)->where('status', 1)->get();
            foreach ($userSubscriptions as $userSubscription) {
                try {
                    $stripeSubscription = Subscription::retrieve($userSubscription->stripe_subscription_id);
                    foreach ($stripeSubscription["items"]["data"] as $item) {
                        Subscription::update(
                            $stripeSubscription->id,
                            [
                                'items' => [
                                    [
                                        'id' => $item->id,
                                        'price' => $newPrice->id,
                                    ],
                                ],
                                'proration_behavior' => 'none',
                            ]
                        );
                    }
                    $this->user_notification($userSubscription->user_id, 'Subscription Updated', 'The plan you have subscribed has been updated');
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    Log::error('Error updating user subscription ' . $userSubscription->id . ': ' . $e->getMessage());
                }
            }
            return to_route('subscription')->with([
                'message' => 'Subscription Plan updated Successfully.',
                'title' => 'Success',
                'type' => 'success'
            ]);

        } catch (\Stripe\Exception\ApiErrorException $e) {
            return back()->withError('Error updating subscription: ' . $e->getMessage());
        } catch (\Exception $e) {
            return back()->withError('An unexpected error occurred while updating the subscription.');
        }
    }
}
