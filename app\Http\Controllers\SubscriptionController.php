<?php

namespace App\Http\Controllers;

use Stripe\Subscription;
use Stripe\Stripe;
use Stripe\Product;
use Stripe\Price;
use App\Models\Subscription as LocalSubscription;
use App\Models\SubscriptionDetail;
use App\Models\UserSubscription;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    public function edit($id)
    {
        $subscription = LocalSubscription::where("ids", $id)->firstOrFail();
        return response()->json($subscription);
    }

    public function update(Request $request, $id)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $request->validate([
            "type" => "required",
            "name" => "required",
            "price" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "booking_fee" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "total_service" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
            "yearly_discount" => "required|numeric|min:0|regex:/^[0-9]+(\.[0-9]+)?$/",
        ]);

        $subscription = LocalSubscription::findOrFail($id);
        $oldProduct = Product::retrieve($subscription->product_id);
        $newPrice = Price::create([
            'unit_amount' => $request->price * 100,
            'currency' => 'usd',
            'recurring' => [
                'interval' => 'month',
            ],
            'product' => $oldProduct->id,
        ]);

        $subscription->type = $request->input('type');
        $subscription->name = $request->input('name');
        $subscription->booking_fee = $request->booking_fee;
        $subscription->consumer_fee = $request->consumer_fee;
        $subscription->yearly_discount = $request->yearly_discount;
        $subscription->pricing_id = $newPrice->id;
        $subscription->price = $request->price;
        $subscription->save();

        if (isset($request->subscription_details)) {
            SubscriptionDetail::where('subscription_id', $request->subscription_id)->delete();

            if (isset($request->subscription_details)) {
                foreach ($request->subscription_details as $detailData) {
                    $detail = new SubscriptionDetail();
                    $detail->plan_id = $request->subscription_id;
                    $detail->feature = $detailData['feature'];
                    $detail->save();
                }
            }
        }

        try {
            $oldPrice = Price::retrieve($subscription->pricing_id);
            $oldPrice->updateAttributes(['active' => false]);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            return back()->withError('Error archiving old price: ' . $e->getMessage());
        }
        $userSubscriptions = UserSubscription::with('stripe')->where('subscription_id', $subscription->id)->where('status', 1)->get();
        foreach ($userSubscriptions as $userSubscription) {
            $subscription = Subscription::retrieve($userSubscription->stripe_subscription_id);
            foreach ($subscription["items"]["data"] as $item) {
                try {
                    $updatedSubscription = Subscription::update(
                        $subscription->id,
                        [
                            'items' => [
                                [
                                    'id' => $item->id,
                                    'price' => $newPrice->id,
                                ],
                            ],
                            'proration_behavior' => 'none',
                        ]
                    );
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    return back()->withError('There was an error updating the subscription.');
                }
            }
            $this->user_notification($userSubscription->user_id, 'Subscription Updated', 'The plan you have subscribed has been updated');
        }
        return to_route('subscription')->with([
            'message' => 'Subscription Plan updated Successfully.',
            'title' => 'Success',
            'type' => 'success'
        ]);
    }
}
